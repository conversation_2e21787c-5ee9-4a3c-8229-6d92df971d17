{"name": "node-langchain-sql", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.10.0", "dependencies": {"@langchain/core": "^0.3.61", "@langchain/google-genai": "^0.2.13", "@langchain/openai": "^0.5.15", "dotenv": "^16.5.0", "express": "^5.1.0", "langchain": "^0.3.29", "openai": "^5.7.0", "pg": "^8.16.2", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25", "zod": "^3.25.67"}}